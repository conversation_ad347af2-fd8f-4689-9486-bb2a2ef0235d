import React, { useState, useEffect } from "react";
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  Button,
  Alert,
  IconButton,
} from "@mui/material";
import {
  AccountBalanceWallet,
  TrendingUp,
  Speed,
  Block,
  Refresh,
  LocalGasStation,
} from "@mui/icons-material";
import { useWallet } from "../../hooks/useWallet";
import { useBlockchain } from "../../hooks/useBlockchain";

interface DashboardStats {
  walletBalance: string;
  totalTransactions: number;
  networkStatus: string;
  blockHeight: number;
  gasPrice: string;
}

const Dashboard: React.FC = () => {
  const { walletState, transactions, loadingTransactions, connectWallet } =
    useWallet();
  const {
    networkStats,
    loading: blockchainLoading,
    error,
    refreshData,
  } = useBlockchain();

  const [stats, setStats] = useState<DashboardStats>({
    walletBalance: "0.00",
    totalTransactions: 0,
    networkStatus: "Disconnected",
    blockHeight: 0,
    gasPrice: "0",
  });

  useEffect(() => {
    updateStats();
  }, [walletState, networkStats, transactions]);

  const updateStats = () => {
    setStats({
      walletBalance: walletState.balance || "0.00",
      totalTransactions: transactions.length,
      networkStatus: walletState.isConnected ? "Connected" : "Disconnected",
      blockHeight: networkStats?.blockHeight || 0,
      gasPrice: networkStats?.gasPrice || "0",
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)} hours ago`;
    return date.toLocaleDateString();
  };

  const truncateHash = (hash: string, length: number = 10) => {
    if (!hash) return "";
    return `${hash.slice(0, length)}...${hash.slice(-4)}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "success";
      case "pending":
        return "warning";
      case "failed":
        return "error";
      default:
        return "default";
    }
  };

  const handleRefresh = () => {
    refreshData();
    if (walletState.address) {
      // Refresh wallet data would be handled by the wallet hook
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
  }> = ({ title, value, icon, color }) => (
    <Card
      sx={{
        height: "100%",
        background: `linear-gradient(135deg, ${color}20, ${color}10)`,
      }}
    >
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ color }}>
              {value}
            </Typography>
          </Box>
          <Box sx={{ color, opacity: 0.7 }}>{icon}</Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>
        <LinearProgress sx={{ mb: 3 }} />
        <Typography>Loading dashboard data...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
        Dashboard
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Wallet Balance"
            value={`${stats.walletBalance} ETH`}
            icon={<AccountBalanceWallet sx={{ fontSize: 40 }} />}
            color="#00d4ff"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Transactions"
            value={stats.totalTransactions}
            icon={<TrendingUp sx={{ fontSize: 40 }} />}
            color="#ff6b35"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Network Status"
            value={stats.networkStatus}
            icon={<Speed sx={{ fontSize: 40 }} />}
            color="#4caf50"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Block Height"
            value={stats.blockHeight.toLocaleString()}
            icon={<Block sx={{ fontSize: 40 }} />}
            color="#9c27b0"
          />
        </Grid>
      </Grid>

      <Card>
        <CardContent>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
            }}
          >
            <Typography variant="h6">Recent Transactions</Typography>
            <IconButton
              onClick={handleRefresh}
              size="small"
              disabled={loadingTransactions}
            >
              <Refresh />
            </IconButton>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {!walletState.isConnected && (
            <Alert
              severity="warning"
              sx={{ mb: 2 }}
              action={
                <Button color="inherit" size="small" onClick={connectWallet}>
                  Connect Wallet
                </Button>
              }
            >
              Please connect your wallet to view real-time transactions
            </Alert>
          )}

          {transactions.length === 0 ? (
            <Typography
              color="textSecondary"
              sx={{ py: 4, textAlign: "center" }}
            >
              {walletState.isConnected
                ? "No transactions found"
                : "Connect wallet to view transactions"}
            </Typography>
          ) : (
            <TableContainer
              component={Paper}
              sx={{ backgroundColor: "transparent" }}
            >
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Hash</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>From/To</TableCell>
                    <TableCell>Value</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Time</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions.slice(0, 10).map((tx, index) => (
                    <TableRow key={tx.hash || index}>
                      <TableCell>
                        <Typography
                          variant="body2"
                          sx={{ fontFamily: "monospace" }}
                        >
                          {truncateHash(tx.hash)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={tx.type}
                          color={tx.type === "sent" ? "error" : "success"}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography
                          variant="body2"
                          sx={{ fontFamily: "monospace" }}
                        >
                          {truncateHash(tx.type === "sent" ? tx.to : tx.from)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {parseFloat(tx.value).toFixed(4)}{" "}
                        {walletState.network?.nativeCurrency?.symbol || "ETH"}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={tx.status}
                          color={getStatusColor(tx.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{formatTimestamp(tx.timestamp)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default Dashboard;
