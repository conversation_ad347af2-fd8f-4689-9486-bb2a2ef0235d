import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@mui/material/styles";
import { CssBaseline } from "@mui/material";
import theme from "./theme/theme";
import Layout from "./components/Layout/Layout";
import Dashboard from "./pages/Dashboard/Dashboard";
import Wallet from "./pages/Wallet/Wallet";
import TransactionHistory from "./pages/TransactionHistory/TransactionHistory";
import BlockExplorer from "./pages/BlockExplorer/BlockExplorer";
import SmartContract from "./pages/SmartContract/SmartContract";

const Settings = () => (
  <div style={{ padding: "20px" }}>
    <h2>Settings</h2>
    <p>Settings page coming soon...</p>
  </div>
);

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/wallet" element={<Wallet />} />
            <Route path="/transactions" element={<TransactionHistory />} />
            <Route path="/contracts" element={<SmartContract />} />
            <Route path="/explorer" element={<BlockExplorer />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Layout>
      </Router>
    </ThemeProvider>
  );
}

export default App;
