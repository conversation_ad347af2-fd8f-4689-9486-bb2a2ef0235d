import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@mui/material/styles";
import {
  CssBaseline,
  Box,
  Typography,
  Drawer,
  AppBar,
  Toolbar,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  AccountBalanceWallet as WalletIcon,
  History as HistoryIcon,
  Code as ContractIcon,
  Search as ExplorerIcon,
  Settings as SettingsIcon,
} from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import theme from "./theme/theme";

// Dashboard với tính năng thực tế
const Dashboard = () => {
  const mockStats = {
    totalBalance: "2.5847 ETH",
    totalTransactions: 156,
    pendingTransactions: 2,
    networkStatus: "Healthy",
    gasPrice: "22 Gwei",
    blockHeight: ********,
  };

  const recentTransactions = [
    {
      id: 1,
      hash: "0x1234...5678",
      amount: "0.5 ETH",
      status: "Success",
      time: "2 mins ago",
    },
    {
      id: 2,
      hash: "0xabcd...efgh",
      amount: "1.2 ETH",
      status: "Pending",
      time: "5 mins ago",
    },
    {
      id: 3,
      hash: "0x9876...5432",
      amount: "0.8 ETH",
      status: "Failed",
      time: "10 mins ago",
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary">
        📊 Bảng Điều Khiển
      </Typography>

      {/* Stats Cards */}
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: 2,
          mb: 4,
        }}
      >
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            💰 Tổng Số Dư
          </Typography>
          <Typography variant="h4">{mockStats.totalBalance}</Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            📝 Tổng Giao Dịch
          </Typography>
          <Typography variant="h4">{mockStats.totalTransactions}</Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            ⏳ Đang Chờ
          </Typography>
          <Typography variant="h4">{mockStats.pendingTransactions}</Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            ⛽ Phí Gas
          </Typography>
          <Typography variant="h4">{mockStats.gasPrice}</Typography>
        </Box>
      </Box>

      {/* Network Status */}
      <Box
        sx={{
          mb: 4,
          p: 2,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom color="primary">
          🌐 Trạng Thái Mạng
        </Typography>
        <Box sx={{ display: "flex", gap: 4 }}>
          <Typography>
            Trạng thái: <span style={{ color: "#4caf50" }}>●</span> Hoạt động
            tốt
          </Typography>
          <Typography>Chiều cao khối: #{mockStats.blockHeight}</Typography>
          <Typography>Phí Gas: {mockStats.gasPrice}</Typography>
        </Box>
      </Box>

      {/* Recent Transactions */}
      <Box
        sx={{
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Box sx={{ p: 2, borderBottom: "1px solid", borderColor: "divider" }}>
          <Typography variant="h6" color="primary">
            📝 Giao Dịch Gần Đây
          </Typography>
        </Box>
        <List>
          {recentTransactions.map((tx) => (
            <ListItem key={tx.id} divider>
              <ListItemText
                primary={
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <Typography variant="body1">{tx.hash}</Typography>
                    <Typography variant="body1" color="primary">
                      {tx.amount}
                    </Typography>
                  </Box>
                }
                secondary={
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mt: 1,
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color:
                          tx.status === "Success"
                            ? "success.main"
                            : tx.status === "Pending"
                            ? "warning.main"
                            : "error.main",
                      }}
                    >
                      {tx.status}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {tx.time}
                    </Typography>
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );
};

const Wallet = () => {
  const [sendAmount, setSendAmount] = React.useState("");
  const [recipientAddress, setRecipientAddress] = React.useState("");

  const walletInfo = {
    address: "******************************************",
    balance: "2.5847 ETH",
    usdValue: "$6,234.56",
    network: "Ethereum Mainnet",
  };

  const handleSendTransaction = () => {
    alert(`Sending ${sendAmount} ETH to ${recipientAddress}`);
    setSendAmount("");
    setRecipientAddress("");
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary">
        💰 Quản Lý Ví Điện Tử
      </Typography>

      {/* Wallet Info Card */}
      <Box
        sx={{
          mb: 4,
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom color="primary">
          👛 Thông Tin Ví
        </Typography>
        <Box sx={{ display: "grid", gap: 2 }}>
          <Box>
            <Typography variant="body2" color="text.secondary">
              Địa chỉ ví:
            </Typography>
            <Typography variant="body1" sx={{ fontFamily: "monospace" }}>
              {walletInfo.address}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", gap: 4 }}>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Số dư:
              </Typography>
              <Typography variant="h5" color="primary">
                {walletInfo.balance}
              </Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Giá trị USD:
              </Typography>
              <Typography variant="h5" color="success.main">
                {walletInfo.usdValue}
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary">
              Mạng:
            </Typography>
            <Typography variant="body1">Ethereum Mainnet</Typography>
          </Box>
        </Box>
      </Box>

      {/* Send Transaction */}
      <Box
        sx={{
          mb: 4,
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom color="primary">
          📤 Gửi Giao Dịch
        </Typography>
        <Box sx={{ display: "grid", gap: 2, maxWidth: 400 }}>
          <Box>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Địa chỉ người nhận:
            </Typography>
            <Box
              sx={{
                p: 1,
                border: "1px solid",
                borderColor: "divider",
                borderRadius: 1,
              }}
            >
              <input
                type="text"
                placeholder="0x..."
                value={recipientAddress}
                onChange={(e) => setRecipientAddress(e.target.value)}
                style={{
                  width: "100%",
                  border: "none",
                  background: "transparent",
                  color: "inherit",
                  outline: "none",
                  fontFamily: "monospace",
                }}
              />
            </Box>
          </Box>
          <Box>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Số lượng (ETH):
            </Typography>
            <Box
              sx={{
                p: 1,
                border: "1px solid",
                borderColor: "divider",
                borderRadius: 1,
              }}
            >
              <input
                type="number"
                placeholder="0.0"
                value={sendAmount}
                onChange={(e) => setSendAmount(e.target.value)}
                style={{
                  width: "100%",
                  border: "none",
                  background: "transparent",
                  color: "inherit",
                  outline: "none",
                }}
              />
            </Box>
          </Box>
          <Box sx={{ mt: 2 }}>
            <Box
              component="button"
              onClick={handleSendTransaction}
              sx={{
                p: 1.5,
                bgcolor: "primary.main",
                color: "primary.contrastText",
                border: "none",
                borderRadius: 1,
                cursor: "pointer",
                width: "100%",
                "&:hover": { bgcolor: "primary.dark" },
              }}
            >
              Gửi Giao Dịch
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Quick Actions */}
      <Box
        sx={{
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom color="primary">
          ⚡ Thao Tác Nhanh
        </Typography>
        <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
          <Box
            component="button"
            sx={{
              p: 2,
              bgcolor: "secondary.main",
              color: "secondary.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": { bgcolor: "secondary.dark" },
            }}
          >
            🔄 Làm Mới Số Dư
          </Box>
          <Box
            component="button"
            sx={{
              p: 2,
              bgcolor: "info.main",
              color: "info.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": { bgcolor: "info.dark" },
            }}
          >
            📋 Sao Chép Địa Chỉ
          </Box>
          <Box
            component="button"
            sx={{
              p: 2,
              bgcolor: "warning.main",
              color: "warning.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": { bgcolor: "warning.dark" },
            }}
          >
            🔗 Xem Trên Explorer
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

const Transactions = () => {
  const transactions = [
    {
      id: 1,
      hash: "******************************************",
      from: "0x742d...C8b4",
      to: "0x8ba1...C4e4",
      amount: "0.5 ETH",
      status: "Success",
      time: "2024-01-15 14:30",
      gas: "21000",
    },
    {
      id: 2,
      hash: "******************************************",
      from: "0x8ba1...C4e4",
      to: "0x742d...C8b4",
      amount: "1.2 ETH",
      status: "Success",
      time: "2024-01-15 13:45",
      gas: "21000",
    },
    {
      id: 3,
      hash: "******************************************",
      from: "0x742d...C8b4",
      to: "0x9cd2...C4e4",
      amount: "0.8 ETH",
      status: "Pending",
      time: "2024-01-15 14:45",
      gas: "21000",
    },
    {
      id: 4,
      hash: "******************************************",
      from: "0x742d...C8b4",
      to: "0x1ef4...C4e4",
      amount: "2.1 ETH",
      status: "Failed",
      time: "2024-01-15 12:30",
      gas: "0",
    },
    {
      id: 5,
      hash: "******************************************",
      from: "0x5678...ABCD",
      to: "0x742d...C8b4",
      amount: "0.3 ETH",
      status: "Success",
      time: "2024-01-15 11:15",
      gas: "21000",
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary">
        📝 Lịch Sử Giao Dịch
      </Typography>

      {/* Filter Controls */}
      <Box
        sx={{
          mb: 3,
          p: 2,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom>
          🔍 Bộ Lọc
        </Typography>
        <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
          <Box
            component="button"
            sx={{
              p: 1,
              bgcolor: "primary.main",
              color: "primary.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
            }}
          >
            Tất Cả
          </Box>
          <Box
            component="button"
            sx={{
              p: 1,
              bgcolor: "success.main",
              color: "success.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
            }}
          >
            Thành Công
          </Box>
          <Box
            component="button"
            sx={{
              p: 1,
              bgcolor: "warning.main",
              color: "warning.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
            }}
          >
            Đang Chờ
          </Box>
          <Box
            component="button"
            sx={{
              p: 1,
              bgcolor: "error.main",
              color: "error.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
            }}
          >
            Thất Bại
          </Box>
        </Box>
      </Box>

      {/* Transaction Table */}
      <Box
        sx={{
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
          overflow: "hidden",
        }}
      >
        <Box
          sx={{
            p: 2,
            borderBottom: "1px solid",
            borderColor: "divider",
            bgcolor: "action.hover",
          }}
        >
          <Typography variant="h6" color="primary">
            📊 Chi Tiết Giao Dịch
          </Typography>
        </Box>

        {/* Table Header */}
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "2fr 1fr 1fr 1fr 1fr 1fr 1fr",
            p: 2,
            borderBottom: "1px solid",
            borderColor: "divider",
            bgcolor: "action.selected",
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            Mã Hash
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Từ
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Đến
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Số Lượng
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Trạng Thái
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Thời Gian
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Gas
          </Typography>
        </Box>

        {/* Table Rows */}
        {transactions.map((tx) => (
          <Box
            key={tx.id}
            sx={{
              display: "grid",
              gridTemplateColumns: "2fr 1fr 1fr 1fr 1fr 1fr 1fr",
              p: 2,
              borderBottom: "1px solid",
              borderColor: "divider",
              "&:hover": { bgcolor: "action.hover" },
            }}
          >
            <Typography
              variant="body2"
              sx={{ fontFamily: "monospace", fontSize: "0.8rem" }}
            >
              {tx.hash}
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: "monospace" }}>
              {tx.from}
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: "monospace" }}>
              {tx.to}
            </Typography>
            <Typography variant="body2" color="primary" fontWeight="bold">
              {tx.amount}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color:
                  tx.status === "Success"
                    ? "success.main"
                    : tx.status === "Pending"
                    ? "warning.main"
                    : "error.main",
                fontWeight: "bold",
              }}
            >
              {tx.status}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {tx.time}
            </Typography>
            <Typography variant="body2">{tx.gas}</Typography>
          </Box>
        ))}
      </Box>

      {/* Summary Stats */}
      <Box
        sx={{
          mt: 3,
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 2,
        }}
      >
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="success.main">
            ✅ Thành Công
          </Typography>
          <Typography variant="h4">3</Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="warning.main">
            ⏳ Đang Chờ
          </Typography>
          <Typography variant="h4">1</Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="error.main">
            ❌ Thất Bại
          </Typography>
          <Typography variant="h4">1</Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            💰 Tổng Khối Lượng
          </Typography>
          <Typography variant="h4">4.9 ETH</Typography>
        </Box>
      </Box>
    </Box>
  );
};

const Contracts = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      📋 Hợp Đồng Thông Minh
    </Typography>
    <Typography>Trang Hợp Đồng Thông Minh đang hoạt động!</Typography>
  </Box>
);

const Explorer = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      🔍 Khám Phá Blockchain
    </Typography>
    <Typography>Trang Khám Phá Blockchain đang hoạt động!</Typography>
  </Box>
);

const Settings = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      ⚙️ Cài Đặt
    </Typography>
    <Typography>Trang Cài Đặt đang hoạt động!</Typography>
  </Box>
);

const drawerWidth = 280;

const menuItems = [
  { text: "Bảng Điều Khiển", icon: <DashboardIcon />, path: "/" },
  { text: "Ví Điện Tử", icon: <WalletIcon />, path: "/wallet" },
  { text: "Lịch Sử Giao Dịch", icon: <HistoryIcon />, path: "/transactions" },
  { text: "Hợp Đồng Thông Minh", icon: <ContractIcon />, path: "/contracts" },
  { text: "Khám Phá Blockchain", icon: <ExplorerIcon />, path: "/explorer" },
  { text: "Cài Đặt", icon: <SettingsIcon />, path: "/settings" },
];

const Layout = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <Box sx={{ display: "flex" }}>
      <AppBar
        position="fixed"
        sx={{
          width: `calc(100% - ${drawerWidth}px)`,
          ml: `${drawerWidth}px`,
          backgroundColor: "background.paper",
          color: "text.primary",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <Toolbar>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            🚀 Hệ Thống Quản Lý Blockchain
          </Typography>
          <Typography variant="body2" color="primary">
            2.5847 ETH | 0x742d...C8b4
          </Typography>
        </Toolbar>
      </AppBar>

      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            boxSizing: "border-box",
            backgroundColor: "background.paper",
          },
        }}
      >
        <Toolbar>
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{ color: "primary.main", fontWeight: "bold" }}
          >
            BlockchainApp
          </Typography>
        </Toolbar>
        <Divider />
        <List>
          {menuItems.map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                selected={location.pathname === item.path}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  "&.Mui-selected": {
                    backgroundColor: "primary.main",
                    color: "primary.contrastText",
                    "&:hover": {
                      backgroundColor: "primary.dark",
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color:
                      location.pathname === item.path
                        ? "inherit"
                        : "text.secondary",
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          bgcolor: "background.default",
          p: 3,
          minHeight: "100vh",
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/wallet" element={<Wallet />} />
            <Route path="/transactions" element={<Transactions />} />
            <Route path="/contracts" element={<Contracts />} />
            <Route path="/explorer" element={<Explorer />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Layout>
      </Router>
    </ThemeProvider>
  );
}

export default App;
