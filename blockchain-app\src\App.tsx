import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@mui/material/styles";
import { CssBaseline, Box, Typography, Button } from "@mui/material";
import theme from "./theme/theme";
import Layout from "./components/Layout/Layout";

// Simple test components
const Dashboard = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom>
      📊 Dashboard
    </Typography>
    <Typography>Dashboard content loading...</Typography>
  </Box>
);

const Wallet = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom>
      💰 Wallet
    </Typography>
    <Typography>Wallet content loading...</Typography>
  </Box>
);

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/wallet" element={<Wallet />} />
          </Routes>
        </Layout>
      </Router>
    </ThemeProvider>
  );
}

export default App;
