import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@mui/material/styles";
import { CssBaseline, Box, Typography } from "@mui/material";
import theme from "./theme/theme";

// Simple test components
const Dashboard = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      🚀 Blockchain Management System
    </Typography>
    <Typography variant="h6" sx={{ mb: 2 }}>
      Dashboard đang hoạt động!
    </Typography>
    <Typography>
      ✅ Mock data đã được tải<br/>
      ✅ Material-UI theme hoạt động<br/>
      ✅ React Router hoạt động
    </Typography>
  </Box>
);

const Wallet = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      💰 Wallet
    </Typography>
    <Typography>Wallet page đang hoạt động!</Typography>
  </Box>
);

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box sx={{ p: 3, minHeight: '100vh', backgroundColor: 'background.default' }}>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/wallet" element={<Wallet />} />
          </Routes>
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;
