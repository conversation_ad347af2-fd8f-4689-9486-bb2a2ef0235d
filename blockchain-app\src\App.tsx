import React, { create<PERSON>ontext, useContext, useState, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@mui/material/styles";
import {
  CssBaseline,
  Box,
  Typography,
  Drawer,
  AppBar,
  Toolbar,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Button,
  LinearProgress,
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  AccountBalanceWallet as WalletIcon,
  History as HistoryIcon,
  Code as ContractIcon,
  Search as ExplorerIcon,
  Settings as SettingsIcon,
} from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import theme from "./theme/theme";

interface Transaction {
  id: string;
  hash: string;
  from: string;
  to: string;
  amount: string;
  status: "Success" | "Pending" | "Failed";
  time: string;
  gas: string;
  timestamp: number;
}

interface Block {
  index: number;
  timestamp: number;
  transactions: Transaction[];
  previousHash: string;
  hash: string;
  nonce: number;
  difficulty: number;
}

interface BlockchainContextType {
  transactions: Transaction[];
  blocks: Block[];
  addTransaction: (
    transaction: Omit<Transaction, "id" | "hash" | "timestamp">
  ) => void;
  updateTransactionStatus: (id: string, status: Transaction["status"]) => void;
  balance: string;
  updateBalance: (newBalance: string) => void;
  mineBlock: () => Promise<{ success: boolean; nonce: number; time: number }>;
  validateBlockchain: () => boolean;
  isMining: boolean;
  pendingTransactions: Transaction[];
}


const calculateHash = (block: Omit<Block, "hash">): string => {
  const data = `${block.index}${block.timestamp}${JSON.stringify(
    block.transactions
  )}${block.previousHash}${block.nonce}`;

  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return `0x${Math.abs(hash).toString(16).padStart(16, "0")}`;
};

const mineBlockWithPoW = async (
  block: Omit<Block, "hash" | "nonce">,
  difficulty: number
): Promise<{ hash: string; nonce: number; time: number }> => {
  const startTime = Date.now();
  let nonce = 0;
  const target = "0".repeat(difficulty);

  while (true) {
    const blockWithNonce = { ...block, nonce };
    const hash = calculateHash(blockWithNonce);

    if (hash.substring(2, 2 + difficulty) === target) {
      const time = Date.now() - startTime;
      return { hash, nonce, time };
    }

    nonce++;

    // Add small delay to simulate mining work and allow UI updates
    if (nonce % 1000 === 0) {
      await new Promise((resolve) => setTimeout(resolve, 1));
    }
  }
};

// Context
const BlockchainContext = createContext<BlockchainContextType | undefined>(
  undefined
);

export const useBlockchain = () => {
  const context = useContext(BlockchainContext);
  if (!context) {
    throw new Error("useBlockchain must be used within BlockchainProvider");
  }
  return context;
};

// BlockchainProvider
const BlockchainProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [transactions, setTransactions] = useState<Transaction[]>([
    {
      id: "1",
      hash: "0x1234567890abcdef1234567890abcdef12345678",
      from: "ha noi",
      to: "ho chi minh",
      amount: "0.5",
      status: "Success",
      time: "2 phút trước",
      gas: "21000",
      timestamp: Date.now() - 120000,
    },
    {
      id: "2",
      hash: "0xabcdef1234567890abcdef1234567890abcdef12",
      from: "ho chi minh",
      to: "ha noi",
      amount: "1.2",
      status: "Pending",
      time: "5 phút trước",
      gas: "21000",
      timestamp: Date.now() - 300000,
    },
  ]);

  const [balance, setBalance] = useState("2.5847");
  const [blocks, setBlocks] = useState<Block[]>([
    {
      index: 0,
      timestamp: Date.now() - 3600000, // 1 hour ago
      transactions: [],
      previousHash: "0",
      hash: "0x000abc123def456789abc123def456789abc123de",
      nonce: 0,
      difficulty: 2,
    },
    {
      index: 1,
      timestamp: Date.now() - 1800000, // 30 minutes ago
      transactions: [
        {
          id: "1",
          hash: "0x1234567890abcdef1234567890abcdef12345678",
          from: "0x742d35Cc6634C0532925a3b8D4C2C4e4C8b4C8b4",
          to: "0x8ba1f109551bD432803012645Hac136c0532925a",
          amount: "0.5",
          status: "Success",
          time: "30 phút trước",
          gas: "21000",
          timestamp: Date.now() - 1800000,
        },
      ],
      previousHash: "0x000abc123def456789abc123def456789abc123de",
      hash: "0x00def456789abc123def456789abc123def456789a",
      nonce: 1056,
      difficulty: 2,
    },
  ]);
  const [isMining, setIsMining] = useState(false);
  const [pendingTransactions, setPendingTransactions] = useState<Transaction[]>(
    []
  );

  const addTransaction = (
    transaction: Omit<Transaction, "id" | "hash" | "timestamp">
  ) => {
    const newTransaction: Transaction = {
      ...transaction,
      id: Date.now().toString(),
      hash: `0x${Math.random().toString(16).substring(2, 42)}`,
      timestamp: Date.now(),
      status: "Pending",
    };

    // Add to both transactions list and pending transactions
    setTransactions((prev) => [newTransaction, ...prev]);
    setPendingTransactions((prev) => [newTransaction, ...prev]);

    // Update balance
    const newBalance = (
      parseFloat(balance) - parseFloat(transaction.amount)
    ).toFixed(4);
    setBalance(newBalance);
  };

  const updateTransactionStatus = (
    id: string,
    status: Transaction["status"]
  ) => {
    setTransactions((prev) =>
      prev.map((tx) => (tx.id === id ? { ...tx, status } : tx))
    );
  };

  const updateBalance = (newBalance: string) => {
    setBalance(newBalance);
  };

  const mineBlock = async (): Promise<{
    success: boolean;
    nonce: number;
    time: number;
  }> => {
    if (isMining) {
      return { success: false, nonce: 0, time: 0 };
    }

    setIsMining(true);

    try {
      // Get pending transactions for the new block
      const transactionsToMine = pendingTransactions.slice(0, 5); // Max 5 transactions per block

      // Create new block
      const newBlock: Omit<Block, "hash" | "nonce"> = {
        index: blocks.length,
        timestamp: Date.now(),
        transactions: transactionsToMine,
        previousHash: blocks[blocks.length - 1]?.hash || "0",
        difficulty: 2, // Difficulty level
      };

      // Mine the block with Proof of Work
      const { hash, nonce, time } = await mineBlockWithPoW(
        newBlock,
        newBlock.difficulty
      );

      const minedBlock: Block = {
        ...newBlock,
        hash,
        nonce,
      };

      // Add block to blockchain
      setBlocks((prev) => [...prev, minedBlock]);

      // Remove mined transactions from pending
      setPendingTransactions((prev) =>
        prev.filter(
          (tx) => !transactionsToMine.some((minedTx) => minedTx.id === tx.id)
        )
      );

      // Update transaction status to Success
      setTransactions((prev) =>
        prev.map((tx) =>
          transactionsToMine.some((minedTx) => minedTx.id === tx.id)
            ? { ...tx, status: "Success" as Transaction["status"] }
            : tx
        )
      );

      setIsMining(false);
      return { success: true, nonce, time };
    } catch (error) {
      setIsMining(false);
      return { success: false, nonce: 0, time: 0 };
    }
  };

  const validateBlockchain = (): boolean => {
    for (let i = 1; i < blocks.length; i++) {
      const currentBlock = blocks[i];
      const previousBlock = blocks[i - 1];

      // Check if current block's hash is valid
      const calculatedHash = calculateHash({
        index: currentBlock.index,
        timestamp: currentBlock.timestamp,
        transactions: currentBlock.transactions,
        previousHash: currentBlock.previousHash,
        nonce: currentBlock.nonce,
        difficulty: currentBlock.difficulty,
      });

      if (currentBlock.hash !== calculatedHash) {
        return false;
      }

      // Check if current block's previous hash matches previous block's hash
      if (currentBlock.previousHash !== previousBlock.hash) {
        return false;
      }

      // Check if hash meets difficulty requirement
      const target = "0".repeat(currentBlock.difficulty);
      if (
        !currentBlock.hash
          .substring(2, 2 + currentBlock.difficulty)
          .startsWith(target)
      ) {
        return false;
      }
    }
    return true;
  };

  return (
    <BlockchainContext.Provider
      value={{
        transactions,
        blocks,
        addTransaction,
        updateTransactionStatus,
        balance,
        updateBalance,
        mineBlock,
        validateBlockchain,
        isMining,
        pendingTransactions,
      }}
    >
      {children}
    </BlockchainContext.Provider>
  );
};

// Dashboard với tính năng thực tế
const Dashboard = () => {
  const { transactions, balance } = useBlockchain();

  const pendingTransactions = transactions.filter(
    (tx) => tx.status === "Pending"
  ).length;
  const recentTransactions = transactions.slice(0, 3);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary">
        📊 Bảng Điều Khiển
      </Typography>

      {/* Stats Cards */}
      <Box
        sx={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: 2,
          mb: 4,
        }}
      >
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            💰 Tổng Số Dư
          </Typography>
          <Typography variant="h4">{balance} ETH</Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            📝 Tổng Giao Dịch
          </Typography>
          <Typography variant="h4">{transactions.length}</Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            ⏳ Đang Chờ
          </Typography>
          <Typography variant="h4">{pendingTransactions}</Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            ⛽ Phí Gas
          </Typography>
          <Typography variant="h4">22 Gwei</Typography>
        </Box>
      </Box>

      {/* Network Status */}
      <Box
        sx={{
          mb: 4,
          p: 2,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom color="primary">
          🌐 Trạng Thái Mạng
        </Typography>
        <Box sx={{ display: "flex", gap: 4 }}>
          <Typography>
            Trạng thái: <span style={{ color: "#4caf50" }}>●</span> Hoạt động
            tốt
          </Typography>
          <Typography>Chiều cao khối: #18500123</Typography>
          <Typography>Phí Gas: 22 Gwei</Typography>
        </Box>
      </Box>

      {/* Recent Transactions */}
      <Box
        sx={{
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Box sx={{ p: 2, borderBottom: "1px solid", borderColor: "divider" }}>
          <Typography variant="h6" color="primary">
            📝 Giao Dịch Gần Đây
          </Typography>
        </Box>
        <List>
          {recentTransactions.map((tx) => (
            <ListItem key={tx.id} divider>
              <ListItemText
                primary={
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      variant="body1"
                      sx={{ fontFamily: "monospace", fontSize: "0.9rem" }}
                    >
                      {tx.hash.slice(0, 20)}...
                    </Typography>
                    <Typography
                      variant="body1"
                      color="primary"
                      fontWeight="bold"
                    >
                      {tx.amount} ETH
                    </Typography>
                  </Box>
                }
                secondary={
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mt: 1,
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color:
                          tx.status === "Success"
                            ? "success.main"
                            : tx.status === "Pending"
                            ? "warning.main"
                            : "error.main",
                        fontWeight: "bold",
                      }}
                    >
                      {tx.status === "Success"
                        ? "Thành công"
                        : tx.status === "Pending"
                        ? "Đang chờ"
                        : "Thất bại"}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {tx.time}
                    </Typography>
                  </Box>
                }
              />
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );
};

const Wallet = () => {
  const { balance, addTransaction } = useBlockchain();
  const [sendAmount, setSendAmount] = React.useState("");
  const [recipientAddress, setRecipientAddress] = React.useState("");
  const [isLoading, setIsLoading] = React.useState(false);

  const walletInfo = {
    address: "ha noi",
    balance: `${balance} ETH`,
    usdValue: `$${(parseFloat(balance) * 2400).toFixed(2)}`,
    network: "FPT",
  };

  const handleSendTransaction = () => {
    if (!recipientAddress || !sendAmount) {
      alert("Vui lòng nhập đầy đủ thông tin!");
      return;
    }

    if (parseFloat(sendAmount) <= 0) {
      alert("Số lượng phải lớn hơn 0!");
      return;
    }

    if (parseFloat(sendAmount) > parseFloat(balance)) {
      alert("Số dư không đủ!");
      return;
    }

    setIsLoading(true);

    // Tạo giao dịch mới
    addTransaction({
      from: walletInfo.address,
      to: recipientAddress,
      amount: sendAmount,
      status: "Pending",
      time: "Vừa xong",
      gas: "21000",
    });

    alert(
      `✅ Đã tạo giao dịch gửi ${sendAmount} ETH đến ${recipientAddress}!\n🔄 Giao dịch đang được xử lý...`
    );

    setSendAmount("");
    setRecipientAddress("");
    setIsLoading(false);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary">
        💰 Quản Lý Ví Điện Tử
      </Typography>

      {/* Wallet Info Card */}
      <Box
        sx={{
          mb: 4,
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom color="primary">
          👛 Thông Tin Ví
        </Typography>
        <Box sx={{ display: "grid", gap: 2 }}>
          <Box>
            <Typography variant="body2" color="text.secondary">
              Địa chỉ ví:
            </Typography>
            <Typography variant="body1" sx={{ fontFamily: "monospace" }}>
              {walletInfo.address}
            </Typography>
          </Box>
          <Box sx={{ display: "flex", gap: 4 }}>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Số dư:
              </Typography>
              <Typography variant="h5" color="primary">
                {walletInfo.balance}
              </Typography>
            </Box>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Giá trị USD:
              </Typography>
              <Typography variant="h5" color="success.main">
                {walletInfo.usdValue}
              </Typography>
            </Box>
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary">
              Mạng:
            </Typography>
            <Typography variant="body1">Ethereum Mainnet</Typography>
          </Box>
        </Box>
      </Box>

      {/* Send Transaction */}
      <Box
        sx={{
          mb: 4,
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom color="primary">
          📤 Gửi Giao Dịch
        </Typography>
        <Box sx={{ display: "grid", gap: 2, maxWidth: 400 }}>
          <Box>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Địa chỉ người nhận:
            </Typography>
            <Box
              sx={{
                p: 1,
                border: "1px solid",
                borderColor: "divider",
                borderRadius: 1,
              }}
            >
              <input
                type="text"
                placeholder="0x..."
                value={recipientAddress}
                onChange={(e) => setRecipientAddress(e.target.value)}
                style={{
                  width: "100%",
                  border: "none",
                  background: "transparent",
                  color: "inherit",
                  outline: "none",
                  fontFamily: "monospace",
                }}
              />
            </Box>
          </Box>
          <Box>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Số lượng (ETH):
            </Typography>
            <Box
              sx={{
                p: 1,
                border: "1px solid",
                borderColor: "divider",
                borderRadius: 1,
              }}
            >
              <input
                type="number"
                placeholder="0.0"
                value={sendAmount}
                onChange={(e) => setSendAmount(e.target.value)}
                style={{
                  width: "100%",
                  border: "none",
                  background: "transparent",
                  color: "inherit",
                  outline: "none",
                }}
              />
            </Box>
          </Box>
          <Box sx={{ mt: 2 }}>
            <Box
              component="button"
              onClick={handleSendTransaction}
              disabled={isLoading}
              sx={{
                p: 1.5,
                bgcolor: isLoading ? "grey.500" : "primary.main",
                color: "primary.contrastText",
                border: "none",
                borderRadius: 1,
                cursor: isLoading ? "not-allowed" : "pointer",
                width: "100%",
                "&:hover": { bgcolor: isLoading ? "grey.500" : "primary.dark" },
                opacity: isLoading ? 0.7 : 1,
              }}
            >
              {isLoading ? "🔄 Đang xử lý..." : "📤 Gửi Giao Dịch"}
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Quick Actions */}
      <Box
        sx={{
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom color="primary">
          ⚡ Thao Tác Nhanh
        </Typography>
        <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
          <Box
            component="button"
            sx={{
              p: 2,
              bgcolor: "secondary.main",
              color: "secondary.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": { bgcolor: "secondary.dark" },
            }}
          >
            🔄 Làm Mới Số Dư
          </Box>
          <Box
            component="button"
            sx={{
              p: 2,
              bgcolor: "info.main",
              color: "info.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": { bgcolor: "info.dark" },
            }}
          >
            📋 Sao Chép Địa Chỉ
          </Box>
          <Box
            component="button"
            sx={{
              p: 2,
              bgcolor: "warning.main",
              color: "warning.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": { bgcolor: "warning.dark" },
            }}
          >
            🔗 Xem Trên Explorer
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

const Transactions = () => {
  const { transactions } = useBlockchain();
  const [filter, setFilter] = React.useState<
    "All" | "Success" | "Pending" | "Failed"
  >("All");

  const filteredTransactions = transactions.filter((tx) => {
    if (filter === "All") return true;
    return tx.status === filter;
  });

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary">
        📝 Lịch Sử Giao Dịch
      </Typography>

      {/* Filter Controls */}
      <Box
        sx={{
          mb: 3,
          p: 2,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom>
          🔍 Bộ Lọc
        </Typography>
        <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
          <Box
            component="button"
            onClick={() => setFilter("All")}
            sx={{
              p: 1,
              bgcolor: filter === "All" ? "primary.main" : "grey.700",
              color: "primary.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": {
                bgcolor: filter === "All" ? "primary.dark" : "grey.600",
              },
            }}
          >
            Tất Cả ({transactions.length})
          </Box>
          <Box
            component="button"
            onClick={() => setFilter("Success")}
            sx={{
              p: 1,
              bgcolor: filter === "Success" ? "success.main" : "grey.700",
              color: "success.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": {
                bgcolor: filter === "Success" ? "success.dark" : "grey.600",
              },
            }}
          >
            Thành Công (
            {transactions.filter((tx) => tx.status === "Success").length})
          </Box>
          <Box
            component="button"
            onClick={() => setFilter("Pending")}
            sx={{
              p: 1,
              bgcolor: filter === "Pending" ? "warning.main" : "grey.700",
              color: "warning.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": {
                bgcolor: filter === "Pending" ? "warning.dark" : "grey.600",
              },
            }}
          >
            Đang Chờ (
            {transactions.filter((tx) => tx.status === "Pending").length})
          </Box>
          <Box
            component="button"
            onClick={() => setFilter("Failed")}
            sx={{
              p: 1,
              bgcolor: filter === "Failed" ? "error.main" : "grey.700",
              color: "error.contrastText",
              border: "none",
              borderRadius: 1,
              cursor: "pointer",
              "&:hover": {
                bgcolor: filter === "Failed" ? "error.dark" : "grey.600",
              },
            }}
          >
            Thất Bại (
            {transactions.filter((tx) => tx.status === "Failed").length})
          </Box>
        </Box>
      </Box>

      {/* Transaction Table */}
      <Box
        sx={{
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
          overflow: "hidden",
        }}
      >
        <Box
          sx={{
            p: 2,
            borderBottom: "1px solid",
            borderColor: "divider",
            bgcolor: "action.hover",
          }}
        >
          <Typography variant="h6" color="primary">
            📊 Chi Tiết Giao Dịch
          </Typography>
        </Box>

        {/* Table Header */}
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "2fr 1fr 1fr 1fr 1fr 1fr 1fr",
            p: 2,
            borderBottom: "1px solid",
            borderColor: "divider",
            bgcolor: "action.selected",
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            Mã Hash
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Từ
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Đến
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Số Lượng
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Trạng Thái
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Thời Gian
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            Gas
          </Typography>
        </Box>

        {/* Table Rows */}
        {filteredTransactions.map((tx) => (
          <Box
            key={tx.id}
            sx={{
              display: "grid",
              gridTemplateColumns: "2fr 1fr 1fr 1fr 1fr 1fr 1fr",
              p: 2,
              borderBottom: "1px solid",
              borderColor: "divider",
              "&:hover": { bgcolor: "action.hover" },
            }}
          >
            <Typography
              variant="body2"
              sx={{ fontFamily: "monospace", fontSize: "0.8rem" }}
            >
              {tx.hash.slice(0, 20)}...
            </Typography>
            <Typography
              variant="body2"
              sx={{ fontFamily: "monospace", fontSize: "0.8rem" }}
            >
              {tx.from.slice(0, 8)}...
            </Typography>
            <Typography
              variant="body2"
              sx={{ fontFamily: "monospace", fontSize: "0.8rem" }}
            >
              {tx.to.slice(0, 8)}...
            </Typography>
            <Typography variant="body2" color="primary" fontWeight="bold">
              {tx.amount} ETH
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color:
                  tx.status === "Success"
                    ? "success.main"
                    : tx.status === "Pending"
                    ? "warning.main"
                    : "error.main",
                fontWeight: "bold",
              }}
            >
              {tx.status === "Success"
                ? "Thành công"
                : tx.status === "Pending"
                ? "Đang chờ"
                : "Thất bại"}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {tx.time}
            </Typography>
            <Typography variant="body2">{tx.gas}</Typography>
          </Box>
        ))}
      </Box>

      {/* Summary Stats */}
      <Box
        sx={{
          mt: 3,
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 2,
        }}
      >
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="success.main">
            ✅ Thành Công
          </Typography>
          <Typography variant="h4">
            {transactions.filter((tx) => tx.status === "Success").length}
          </Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="warning.main">
            ⏳ Đang Chờ
          </Typography>
          <Typography variant="h4">
            {transactions.filter((tx) => tx.status === "Pending").length}
          </Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="error.main">
            ❌ Thất Bại
          </Typography>
          <Typography variant="h4">
            {transactions.filter((tx) => tx.status === "Failed").length}
          </Typography>
        </Box>
        <Box
          sx={{
            p: 2,
            bgcolor: "background.paper",
            borderRadius: 2,
            border: "1px solid",
            borderColor: "divider",
          }}
        >
          <Typography variant="h6" color="primary">
            💰 Tổng Khối Lượng
          </Typography>
          <Typography variant="h4">
            {transactions
              .reduce((total, tx) => total + parseFloat(tx.amount), 0)
              .toFixed(2)}{" "}
            ETH
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

// Mining Component với Proof of Work
const Mining = () => {
  const { mineBlock, isMining, pendingTransactions, blocks } = useBlockchain();
  const [miningResult, setMiningResult] = useState<{
    success: boolean;
    nonce: number;
    time: number;
  } | null>(null);

  const handleMineBlock = async () => {
    setMiningResult(null);
    const result = await mineBlock();
    setMiningResult(result);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary">
        ⛏️ Đào Block (Mining)
      </Typography>
      <Typography variant="body1" sx={{ mb: 3, color: "text.secondary" }}>
        Mô phỏng quá trình đào block với thuật toán Proof of Work
      </Typography>

      {/* Mining Status */}
      <Box
        sx={{
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
          mb: 3,
        }}
      >
        <Typography variant="h6" gutterBottom>
          📊 Trạng Thái Mining
        </Typography>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: 2,
          }}
        >
          <Box>
            <Typography variant="body2" color="text.secondary">
              Giao dịch chờ xử lý
            </Typography>
            <Typography variant="h5" color="warning.main">
              {pendingTransactions.length}
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary">
              Tổng số block
            </Typography>
            <Typography variant="h5" color="primary">
              {blocks.length}
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary">
              Độ khó hiện tại
            </Typography>
            <Typography variant="h5" color="info.main">
              2
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Mining Action */}
      <Box
        sx={{
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
          mb: 3,
        }}
      >
        <Typography variant="h6" gutterBottom>
          🚀 Bắt Đầu Mining
        </Typography>
        <Button
          variant="contained"
          size="large"
          onClick={handleMineBlock}
          disabled={isMining || pendingTransactions.length === 0}
          sx={{ mr: 2 }}
        >
          {isMining ? "Đang đào..." : "Đào Block Mới"}
        </Button>
        {isMining && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              ⚡ Đang tính toán Proof of Work...
            </Typography>
            <Box sx={{ mt: 1, display: "flex", alignItems: "center" }}>
              <Box sx={{ width: "100%", mr: 1 }}>
                <LinearProgress />
              </Box>
            </Box>
          </Box>
        )}
      </Box>

      {/* Mining Result */}
      {miningResult && (
        <Box
          sx={{
            p: 3,
            bgcolor: miningResult.success ? "success.dark" : "error.dark",
            borderRadius: 2,
            mb: 3,
          }}
        >
          <Typography variant="h6" gutterBottom color="white">
            {miningResult.success
              ? "✅ Mining Thành Công!"
              : "❌ Mining Thất Bại"}
          </Typography>
          {miningResult.success && (
            <Box>
              <Typography variant="body1" color="white">
                🔢 Nonce: {miningResult.nonce.toLocaleString()}
              </Typography>
              <Typography variant="body1" color="white">
                ⏱️ Thời gian: {miningResult.time}ms
              </Typography>
            </Box>
          )}
        </Box>
      )}

      {/* Pending Transactions */}
      <Box
        sx={{
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="h6" gutterBottom>
          📋 Giao Dịch Chờ Xử Lý
        </Typography>
        {pendingTransactions.length === 0 ? (
          <Typography variant="body2" color="text.secondary">
            Không có giao dịch nào chờ xử lý
          </Typography>
        ) : (
          pendingTransactions.slice(0, 5).map((tx) => (
            <Box
              key={tx.id}
              sx={{
                p: 2,
                mb: 1,
                bgcolor: "background.default",
                borderRadius: 1,
                border: "1px solid",
                borderColor: "divider",
              }}
            >
              <Typography variant="body2">
                <strong>From:</strong> {tx.from.substring(0, 10)}...
              </Typography>
              <Typography variant="body2">
                <strong>To:</strong> {tx.to.substring(0, 10)}...
              </Typography>
              <Typography variant="body2">
                <strong>Amount:</strong> {tx.amount} ETH
              </Typography>
            </Box>
          ))
        )}
      </Box>
    </Box>
  );
};

const Contracts = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      📋 Hợp Đồng Thông Minh
    </Typography>
    <Typography>Trang Hợp Đồng Thông Minh đang hoạt động!</Typography>
  </Box>
);

// Blockchain Explorer với trực quan hóa
const Explorer = () => {
  const { blocks, validateBlockchain } = useBlockchain();
  const [isValid, setIsValid] = useState<boolean | null>(null);

  const handleValidateBlockchain = () => {
    const result = validateBlockchain();
    setIsValid(result);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary">
        🔍 Khám Phá Blockchain
      </Typography>
      <Typography variant="body1" sx={{ mb: 3, color: "text.secondary" }}>
        Trực quan hóa chuỗi blockchain và kiểm tra tính hợp lệ
      </Typography>

      {/* Validation Section */}
      <Box
        sx={{
          p: 3,
          bgcolor: "background.paper",
          borderRadius: 2,
          border: "1px solid",
          borderColor: "divider",
          mb: 3,
        }}
      >
        <Typography variant="h6" gutterBottom>
          🔐 Kiểm Tra Tính Hợp Lệ
        </Typography>
        <Button
          variant="contained"
          onClick={handleValidateBlockchain}
          sx={{ mr: 2 }}
        >
          Validate Blockchain
        </Button>
        {isValid !== null && (
          <Box sx={{ mt: 2 }}>
            <Typography
              variant="h6"
              color={isValid ? "success.main" : "error.main"}
            >
              {isValid
                ? "✅ Blockchain hợp lệ!"
                : "❌ Blockchain không hợp lệ!"}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Blockchain Visualization */}
      <Typography variant="h6" gutterBottom>
        🔗 Chuỗi Blockchain
      </Typography>
      <Box
        sx={{
          display: "flex",
          overflowX: "auto",
          gap: 2,
          pb: 2,
          minHeight: "300px",
        }}
      >
        {blocks.map((block, index) => (
          <Box
            key={block.index}
            sx={{
              minWidth: "300px",
              p: 3,
              bgcolor: "primary.dark",
              borderRadius: 2,
              border: "2px solid",
              borderColor: "primary.main",
              position: "relative",
              color: "white",
            }}
          >
            {/* Block Header */}
            <Typography variant="h6" gutterBottom>
              Block #{block.index}
            </Typography>

            {/* Block Details */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Hash:</strong>
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: "monospace",
                  bgcolor: "rgba(255,255,255,0.1)",
                  p: 1,
                  borderRadius: 1,
                  wordBreak: "break-all",
                }}
              >
                {block.hash}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Previous Hash:</strong>
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: "monospace",
                  bgcolor: "rgba(255,255,255,0.1)",
                  p: 1,
                  borderRadius: 1,
                  wordBreak: "break-all",
                }}
              >
                {block.previousHash}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Nonce:</strong> {block.nonce}
              </Typography>
              <Typography variant="body2">
                <strong>Transactions:</strong> {block.transactions.length}
              </Typography>
              <Typography variant="body2">
                <strong>Timestamp:</strong>{" "}
                {new Date(block.timestamp).toLocaleString("vi-VN")}
              </Typography>
            </Box>

            {/* Connection Arrow */}
            {index < blocks.length - 1 && (
              <Box
                sx={{
                  position: "absolute",
                  right: "-20px",
                  top: "50%",
                  transform: "translateY(-50%)",
                  fontSize: "24px",
                  color: "primary.main",
                }}
              >
                →
              </Box>
            )}
          </Box>
        ))}
      </Box>

      {/* Block Details */}
      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          📊 Thống Kê Blockchain
        </Typography>
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: 2,
          }}
        >
          <Box
            sx={{
              p: 2,
              bgcolor: "background.paper",
              borderRadius: 2,
              border: "1px solid",
              borderColor: "divider",
            }}
          >
            <Typography variant="h6" color="primary">
              🔗 Tổng Blocks
            </Typography>
            <Typography variant="h4">{blocks.length}</Typography>
          </Box>
          <Box
            sx={{
              p: 2,
              bgcolor: "background.paper",
              borderRadius: 2,
              border: "1px solid",
              borderColor: "divider",
            }}
          >
            <Typography variant="h6" color="success.main">
              ✅ Tổng Giao Dịch
            </Typography>
            <Typography variant="h4">
              {blocks.reduce(
                (total, block) => total + block.transactions.length,
                0
              )}
            </Typography>
          </Box>
          <Box
            sx={{
              p: 2,
              bgcolor: "background.paper",
              borderRadius: 2,
              border: "1px solid",
              borderColor: "divider",
            }}
          >
            <Typography variant="h6" color="info.main">
              ⚡ Độ Khó
            </Typography>
            <Typography variant="h4">
              {blocks[blocks.length - 1]?.difficulty || 0}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

const Settings = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      ⚙️ Cài Đặt
    </Typography>
    <Typography>Trang Cài Đặt đang hoạt động!</Typography>
  </Box>
);

const drawerWidth = 280;

const menuItems = [
  { text: "Bảng Điều Khiển", icon: <DashboardIcon />, path: "/" },
  { text: "Ví Điện Tử", icon: <WalletIcon />, path: "/wallet" },
  { text: "Lịch Sử Giao Dịch", icon: <HistoryIcon />, path: "/transactions" },
  { text: "Đào Block", icon: <ContractIcon />, path: "/mining" },
  { text: "Khám Phá Blockchain", icon: <ExplorerIcon />, path: "/explorer" },
  { text: "Hợp Đồng Thông Minh", icon: <ContractIcon />, path: "/contracts" },
  { text: "Cài Đặt", icon: <SettingsIcon />, path: "/settings" },
];

const Layout = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <Box sx={{ display: "flex" }}>
      <AppBar
        position="fixed"
        sx={{
          width: `calc(100% - ${drawerWidth}px)`,
          ml: `${drawerWidth}px`,
          backgroundColor: "background.paper",
          color: "text.primary",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <Toolbar>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            🚀 Hệ Thống Quản Lý Blockchain
          </Typography>
          <Typography variant="body2" color="primary">
            2.5847 ETH | 0x742d...C8b4
          </Typography>
        </Toolbar>
      </AppBar>

      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            boxSizing: "border-box",
            backgroundColor: "background.paper",
          },
        }}
      >
        <Toolbar>
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{ color: "primary.main", fontWeight: "bold" }}
          >
            BlockchainApp
          </Typography>
        </Toolbar>
        <Divider />
        <List>
          {menuItems.map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                selected={location.pathname === item.path}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  "&.Mui-selected": {
                    backgroundColor: "primary.main",
                    color: "primary.contrastText",
                    "&:hover": {
                      backgroundColor: "primary.dark",
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color:
                      location.pathname === item.path
                        ? "inherit"
                        : "text.secondary",
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          bgcolor: "background.default",
          p: 3,
          minHeight: "100vh",
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <BlockchainProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/wallet" element={<Wallet />} />
              <Route path="/transactions" element={<Transactions />} />
              <Route path="/mining" element={<Mining />} />
              <Route path="/explorer" element={<Explorer />} />
              <Route path="/contracts" element={<Contracts />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Layout>
        </Router>
      </BlockchainProvider>
    </ThemeProvider>
  );
}

export default App;
