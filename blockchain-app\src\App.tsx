import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@mui/material/styles";
import {
  CssBaseline,
  Box,
  Typography,
  Drawer,
  AppBar,
  Toolbar,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  AccountBalanceWallet as WalletIcon,
  History as HistoryIcon,
  Code as ContractIcon,
  Search as ExplorerIcon,
  Settings as SettingsIcon,
} from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import theme from "./theme/theme";

// Simple test components
const Dashboard = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      🚀 Blockchain Management System
    </Typography>
    <Typography variant="h6" sx={{ mb: 2 }}>
      Dashboard đang hoạt động!
    </Typography>
    <Typography>
      ✅ Mock data đã được tải
      <br />
      ✅ Material-UI theme hoạt động
      <br />✅ React Router hoạt động
    </Typography>
  </Box>
);

const Wallet = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      💰 Wallet
    </Typography>
    <Typography>Wallet page đang hoạt động!</Typography>
  </Box>
);

const Transactions = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      📝 Transaction History
    </Typography>
    <Typography>Transaction History page đang hoạt động!</Typography>
  </Box>
);

const Contracts = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      📋 Smart Contracts
    </Typography>
    <Typography>Smart Contracts page đang hoạt động!</Typography>
  </Box>
);

const Explorer = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      🔍 Block Explorer
    </Typography>
    <Typography>Block Explorer page đang hoạt động!</Typography>
  </Box>
);

const Settings = () => (
  <Box sx={{ p: 3 }}>
    <Typography variant="h4" gutterBottom color="primary">
      ⚙️ Settings
    </Typography>
    <Typography>Settings page đang hoạt động!</Typography>
  </Box>
);

const drawerWidth = 280;

const menuItems = [
  { text: "Dashboard", icon: <DashboardIcon />, path: "/" },
  { text: "Wallet", icon: <WalletIcon />, path: "/wallet" },
  { text: "Transaction History", icon: <HistoryIcon />, path: "/transactions" },
  { text: "Smart Contracts", icon: <ContractIcon />, path: "/contracts" },
  { text: "Block Explorer", icon: <ExplorerIcon />, path: "/explorer" },
  { text: "Settings", icon: <SettingsIcon />, path: "/settings" },
];

const Layout = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <Box sx={{ display: "flex" }}>
      <AppBar
        position="fixed"
        sx={{
          width: `calc(100% - ${drawerWidth}px)`,
          ml: `${drawerWidth}px`,
          backgroundColor: "background.paper",
          color: "text.primary",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <Toolbar>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            🚀 Blockchain Management System
          </Typography>
          <Typography variant="body2" color="primary">
            2.5847 ETH | 0x742d...C8b4
          </Typography>
        </Toolbar>
      </AppBar>

      <Drawer
        variant="permanent"
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            boxSizing: "border-box",
            backgroundColor: "background.paper",
          },
        }}
      >
        <Toolbar>
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{ color: "primary.main", fontWeight: "bold" }}
          >
            BlockchainApp
          </Typography>
        </Toolbar>
        <Divider />
        <List>
          {menuItems.map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                selected={location.pathname === item.path}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  "&.Mui-selected": {
                    backgroundColor: "primary.main",
                    color: "primary.contrastText",
                    "&:hover": {
                      backgroundColor: "primary.dark",
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color:
                      location.pathname === item.path
                        ? "inherit"
                        : "text.secondary",
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          bgcolor: "background.default",
          p: 3,
          minHeight: "100vh",
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/wallet" element={<Wallet />} />
            <Route path="/transactions" element={<Transactions />} />
            <Route path="/contracts" element={<Contracts />} />
            <Route path="/explorer" element={<Explorer />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Layout>
      </Router>
    </ThemeProvider>
  );
}

export default App;
